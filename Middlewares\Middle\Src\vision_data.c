#include "vision_data.h"
#include "motion_control.h"

// 全局变量定义
VisionData_t vision_data;

// 内部函数声明
static void VisionData_ProcessServoCommand(uint8_t *data, uint8_t length);
static void VisionData_ProcessControlCommand(uint8_t command);

/**
 * @brief 视觉数据初始化
 */
void VisionData_Init(void) {
    memset(&vision_data, 0, sizeof(VisionData_t));
    vision_data.current_rectangle.is_valid = false;
    vision_data.connection_active = false;
    vision_data.last_update_time = HAL_GetTick();
}

/**
 * @brief 处理数据包
 */
bool VisionData_ProcessPacket(uint8_t *data, uint8_t length) {
    if (!VisionData_ValidatePacket(data, length)) {
        vision_data.error_count++;
        return false;
    }
    
    vision_data.packet_count++;
    vision_data.last_update_time = HAL_GetTick();
    vision_data.connection_active = true;
    
    uint8_t command = data[2];
    
    switch (command) {
        case VISION_CMD_SERVO_X:
        case VISION_CMD_SERVO_Y:
            VisionData_ProcessServoCommand(data, length);
            break;
            
        case VISION_CMD_RECTANGLE:
            return VisionData_ParseRectangle(data, length);
            
        case VISION_CMD_RESET:
        case VISION_CMD_START_BORDER:
        case VISION_CMD_START_TAPE:
        case VISION_CMD_STOP:
            VisionData_ProcessControlCommand(command);
            break;
            
        case VISION_CMD_STATUS_REQ:
            VisionData_SendStatus();
            break;
            
        default:
            vision_data.error_count++;
            return false;
    }
    
    return true;
}

/**
 * @brief 验证数据包
 */
bool VisionData_ValidatePacket(uint8_t *data, uint8_t length) {
    if (length < 4) {
        return false;
    }
    
    // 检查包头
    if (data[0] != 0xAA) {
        return false;
    }
    
    // 检查长度
    if (data[1] != length) {
        return false;
    }
    
    // 检查校验和
    uint8_t calculated_checksum = VisionData_CalculateChecksum(data, length - 1);
    if (data[length - 1] != calculated_checksum) {
        return false;
    }
    
    return true;
}

/**
 * @brief 解析矩形数据
 */
bool VisionData_ParseRectangle(uint8_t *data, uint8_t length) {
    // 矩形数据包格式: [0xAA][长度][0x03][x1][y1][x2][y2][x3][y3][x4][y4][置信度][校验和]
    // 每个坐标4字节(float)，置信度4字节(float)
    if (length != 39) { // 1+1+1+32+4 = 39字节
        vision_data.error_count++;
        return false;
    }
    
    VisionRectangle_t new_rect;
    uint8_t *coord_data = &data[3];
    
    // 解析四个顶点坐标
    for (int i = 0; i < 4; i++) {
        new_rect.points[i].x = hex_to_float(coord_data, i * 8);
        new_rect.points[i].y = hex_to_float(coord_data, i * 8 + 4);
    }
    
    // 解析置信度
    new_rect.confidence = hex_to_float(coord_data, 32);
    
    // 设置时间戳和有效性
    new_rect.timestamp = HAL_GetTick();
    new_rect.is_valid = (new_rect.confidence > 0.5f); // 置信度阈值
    
    // 数据验证：检查坐标是否合理
    for (int i = 0; i < 4; i++) {
        if (new_rect.points[i].x < 0 || new_rect.points[i].x > screen_config.width ||
            new_rect.points[i].y < 0 || new_rect.points[i].y > screen_config.height) {
            new_rect.is_valid = false;
            break;
        }
    }
    
    // 更新当前矩形数据
    if (new_rect.is_valid) {
        vision_data.current_rectangle = new_rect;
        
        // 如果运动控制系统需要矩形数据，则更新
        if (MotionControl_GetState() == MOTION_STATE_IDLE) {
            MotionControl_SetTapeRectangle(new_rect.points);
        }
        
        VisionData_SendAck(VISION_CMD_RECTANGLE);
        return true;
    } else {
        vision_data.error_count++;
        VisionData_SendError(0x01); // 数据无效错误
        return false;
    }
}

/**
 * @brief 处理舵机控制命令(保持原有功能)
 */
static void VisionData_ProcessServoCommand(uint8_t *data, uint8_t length) {
    if (length != 7) {
        vision_data.error_count++;
        return;
    }
    
    uint8_t servo_id = data[2];
    float angle = hex_to_float(data, 3);
    
    if (servo_id == VISION_CMD_SERVO_X) {
        Servo_Set(2, angle); // X轴舵机
    } else if (servo_id == VISION_CMD_SERVO_Y) {
        Servo_Set(1, angle); // Y轴舵机
    }
    
    VisionData_SendAck(servo_id);
}

/**
 * @brief 处理控制命令
 */
static void VisionData_ProcessControlCommand(uint8_t command) {
    MotionCommand_t motion_cmd = MOTION_CMD_NONE;
    
    switch (command) {
        case VISION_CMD_RESET:
            motion_cmd = MOTION_CMD_RESET;
            break;
            
        case VISION_CMD_START_BORDER:
            motion_cmd = MOTION_CMD_START_BORDER;
            break;
            
        case VISION_CMD_START_TAPE:
            motion_cmd = MOTION_CMD_START_TAPE;
            break;
            
        case VISION_CMD_STOP:
            motion_cmd = MOTION_CMD_STOP;
            break;
    }
    
    if (motion_cmd != MOTION_CMD_NONE) {
        MotionControl_SendCommand(motion_cmd);
        VisionData_SendAck(command);
    }
}

/**
 * @brief 获取矩形数据
 */
VisionRectangle_t VisionData_GetRectangle(void) {
    return vision_data.current_rectangle;
}

/**
 * @brief 检查矩形数据是否有效
 */
bool VisionData_IsRectangleValid(void) {
    uint32_t current_time = HAL_GetTick();
    uint32_t data_age = current_time - vision_data.current_rectangle.timestamp;
    
    // 数据超过5秒认为无效
    return vision_data.current_rectangle.is_valid && (data_age < 5000);
}

/**
 * @brief 获取上次更新时间
 */
uint32_t VisionData_GetLastUpdateTime(void) {
    return vision_data.last_update_time;
}

/**
 * @brief 检查连接状态
 */
bool VisionData_IsConnected(void) {
    uint32_t current_time = HAL_GetTick();
    uint32_t time_since_update = current_time - vision_data.last_update_time;
    
    // 超过3秒没有数据认为连接断开
    return time_since_update < 3000;
}

/**
 * @brief 获取包计数
 */
uint32_t VisionData_GetPacketCount(void) {
    return vision_data.packet_count;
}

/**
 * @brief 获取错误计数
 */
uint32_t VisionData_GetErrorCount(void) {
    return vision_data.error_count;
}

/**
 * @brief 获取错误率
 */
float VisionData_GetErrorRate(void) {
    if (vision_data.packet_count == 0) {
        return 0.0f;
    }
    return (float)vision_data.error_count / (vision_data.packet_count + vision_data.error_count);
}

/**
 * @brief 计算校验和
 */
uint8_t VisionData_CalculateChecksum(uint8_t *data, uint8_t length) {
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 发送状态信息
 */
void VisionData_SendStatus(void) {
    uint8_t status_packet[16];
    status_packet[0] = 0xAA;        // 包头
    status_packet[1] = 16;          // 长度
    status_packet[2] = 0x80;        // 状态响应命令
    
    // 运动控制状态
    status_packet[3] = (uint8_t)MotionControl_GetState();
    
    // 统计信息
    uint32_t runtime = MotionControl_GetRuntime();
    memcpy(&status_packet[4], &runtime, 4);
    
    float max_dev = MotionControl_GetMaxDeviation();
    memcpy(&status_packet[8], &max_dev, 4);
    
    int score = MotionControl_GetScore();
    memcpy(&status_packet[12], &score, 4);
    
    // 校验和
    status_packet[15] = VisionData_CalculateChecksum(status_packet, 15);
    
    // 发送数据包
    HAL_UART_Transmit(&huart1, status_packet, 16, 100);
}

/**
 * @brief 发送确认
 */
void VisionData_SendAck(uint8_t command) {
    uint8_t ack_packet[4];
    ack_packet[0] = 0xAA;           // 包头
    ack_packet[1] = 4;              // 长度
    ack_packet[2] = 0x81;           // ACK命令
    ack_packet[3] = command;        // 原命令
    
    HAL_UART_Transmit(&huart1, ack_packet, 4, 100);
}

/**
 * @brief 发送错误
 */
void VisionData_SendError(uint8_t error_code) {
    uint8_t error_packet[5];
    error_packet[0] = 0xAA;         // 包头
    error_packet[1] = 5;            // 长度
    error_packet[2] = 0x82;         // 错误命令
    error_packet[3] = error_code;   // 错误代码
    error_packet[4] = VisionData_CalculateChecksum(error_packet, 4);
    
    HAL_UART_Transmit(&huart1, error_packet, 5, 100);
}
