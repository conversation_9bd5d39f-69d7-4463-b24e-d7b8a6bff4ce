#include "system_test.h"

// 全局变量定义
TestSuite_t test_suite;

/**
 * @brief 测试初始化
 */
void SystemTest_Init(void) {
    memset(&test_suite, 0, sizeof(TestSuite_t));
    DebugMonitor_LogInfo("System Test Initialized");
}

/**
 * @brief 运行所有测试
 */
void SystemTest_RunAll(void) {
    DebugMonitor_LogInfo("=== Running All Tests ===");
    
    test_suite.test_count = 0;
    test_suite.passed_count = 0;
    test_suite.total_duration = 0;
    
    // 运行各项测试
    SystemTest_RunSingle(TEST_TYPE_SERVO_BASIC);
    SystemTest_RunSingle(TEST_TYPE_COORDINATE_CONVERT);
    SystemTest_RunSingle(TEST_TYPE_PATH_GENERATION);
    SystemTest_RunSingle(TEST_TYPE_RESET_FUNCTION);
    SystemTest_RunSingle(TEST_TYPE_BORDER_MOVEMENT);
    SystemTest_RunSingle(TEST_TYPE_VISION_DATA);
    
    // 打印结果
    SystemTest_PrintResults();
}

/**
 * @brief 运行单个测试
 */
void SystemTest_RunSingle(TestType_t test_type) {
    if (test_suite.test_count >= 10) {
        return;
    }
    
    TestResult_t *result = &test_suite.results[test_suite.test_count];
    result->test_type = test_type;
    
    uint32_t start_time = HAL_GetTick();
    bool passed = false;
    
    switch (test_type) {
        case TEST_TYPE_SERVO_BASIC:
            passed = SystemTest_ServoBasic();
            strcpy(result->description, "Basic Servo Control");
            break;
            
        case TEST_TYPE_COORDINATE_CONVERT:
            passed = SystemTest_CoordinateConvert();
            strcpy(result->description, "Coordinate Conversion");
            break;
            
        case TEST_TYPE_PATH_GENERATION:
            passed = SystemTest_PathGeneration();
            strcpy(result->description, "Path Generation");
            break;
            
        case TEST_TYPE_RESET_FUNCTION:
            passed = SystemTest_ResetFunction();
            strcpy(result->description, "Reset Function");
            break;
            
        case TEST_TYPE_BORDER_MOVEMENT:
            passed = SystemTest_BorderMovement();
            strcpy(result->description, "Border Movement");
            break;
            
        case TEST_TYPE_VISION_DATA:
            passed = SystemTest_VisionData();
            strcpy(result->description, "Vision Data Processing");
            break;
            
        default:
            strcpy(result->description, "Unknown Test");
            break;
    }
    
    result->duration_ms = HAL_GetTick() - start_time;
    result->passed = passed;
    
    test_suite.test_count++;
    if (passed) {
        test_suite.passed_count++;
    }
    test_suite.total_duration += result->duration_ms;
    
    DebugMonitor_LogInfo("Test %s: %s (%.2fs)", 
                        result->description,
                        passed ? "PASS" : "FAIL",
                        result->duration_ms / 1000.0f);
}

/**
 * @brief 基本舵机测试
 */
bool SystemTest_ServoBasic(void) {
    DebugMonitor_LogDebug("Testing basic servo control...");
    
    // 测试舵机设置和读取
    float test_angles[] = {10.0f, 25.0f, 40.0f};
    
    for (int i = 0; i < 3; i++) {
        // 设置X轴舵机
        Servo_Set(2, test_angles[i]);
        osDelay(500);
        
        // 检查是否接近目标角度
        if (fabs(servo_down.target - test_angles[i]) > 0.1f) {
            DebugMonitor_LogError("Servo X angle mismatch: expected %.1f, got %.1f", 
                                 test_angles[i], servo_down.target);
            return false;
        }
        
        // 设置Y轴舵机
        Servo_Set(1, test_angles[i]);
        osDelay(500);
        
        // 检查是否接近目标角度
        if (fabs(servo_up.target - test_angles[i]) > 0.1f) {
            DebugMonitor_LogError("Servo Y angle mismatch: expected %.1f, got %.1f", 
                                 test_angles[i], servo_up.target);
            return false;
        }
    }
    
    return true;
}

/**
 * @brief 坐标转换测试
 */
bool SystemTest_CoordinateConvert(void) {
    DebugMonitor_LogDebug("Testing coordinate conversion...");
    
    // 测试几个关键点的坐标转换
    Point_t test_points[] = {
        {0.0f, 0.0f},           // 左下角
        {30.0f, 20.0f},         // 右上角
        {15.0f, 10.0f},         // 中心点
        {30.0f, 0.0f},          // 右下角
        {0.0f, 20.0f}           // 左上角
    };
    
    for (int i = 0; i < 5; i++) {
        Point_t original = test_points[i];
        
        // 坐标 -> 角度 -> 坐标
        ServoAngle_t angle = Point_To_ServoAngle(original);
        Point_t converted = ServoAngle_To_Point(angle);
        
        // 检查转换误差
        float error = Point_Distance(original, converted);
        if (error > 0.5f) {  // 允许0.5cm误差
            DebugMonitor_LogError("Coordinate conversion error: %.2f cm", error);
            return false;
        }
    }
    
    return true;
}

/**
 * @brief 路径生成测试
 */
bool SystemTest_PathGeneration(void) {
    DebugMonitor_LogDebug("Testing path generation...");
    
    Path_t test_path;
    
    // 测试屏幕边线路径生成
    Path_GenerateScreenBorder(&test_path, 30000, true);
    if (test_path.count == 0) {
        DebugMonitor_LogError("Screen border path generation failed");
        return false;
    }
    
    // 测试矩形路径生成
    Point_t rect_points[4] = {
        {5.0f, 5.0f},
        {25.0f, 5.0f},
        {25.0f, 15.0f},
        {5.0f, 15.0f}
    };
    Rectangle_t test_rect = Rectangle_Create(rect_points);
    Path_GenerateRectangle(&test_path, test_rect, 30000, true);
    if (test_path.count == 0) {
        DebugMonitor_LogError("Rectangle path generation failed");
        return false;
    }
    
    // 测试复位路径生成
    Point_t start_point = {20.0f, 15.0f};
    Path_GenerateReturnToOrigin(&test_path, start_point, 3000);
    if (test_path.count == 0) {
        DebugMonitor_LogError("Return to origin path generation failed");
        return false;
    }
    
    return true;
}

/**
 * @brief 复位功能测试
 */
bool SystemTest_ResetFunction(void) {
    DebugMonitor_LogDebug("Testing reset function...");
    
    // 移动到一个随机位置
    Point_t random_pos = {20.0f, 15.0f};
    MotionControl_SetTargetPosition(random_pos);
    osDelay(1000);
    
    // 执行复位
    MotionControl_SendCommand(MOTION_CMD_RESET);
    
    // 等待复位完成
    if (!SystemTest_WaitForCompletion(5000)) {
        DebugMonitor_LogError("Reset function timeout");
        return false;
    }
    
    // 检查是否回到原点
    Point_t current_pos = MotionControl_GetCurrentPosition();
    float error = Point_Distance(current_pos, screen_config.origin);
    if (error > 2.0f) {  // 要求误差≤2cm
        DebugMonitor_LogError("Reset position error: %.2f cm", error);
        return false;
    }
    
    return true;
}

/**
 * @brief 边线移动测试
 */
bool SystemTest_BorderMovement(void) {
    DebugMonitor_LogDebug("Testing border movement...");
    
    // 启动边线移动
    MotionControl_SendCommand(MOTION_CMD_START_BORDER);
    
    // 等待一段时间观察运动
    osDelay(5000);  // 观察5秒
    
    // 检查是否在运行
    if (MotionControl_GetState() != MOTION_STATE_SCREEN_BORDER) {
        DebugMonitor_LogError("Border movement not started");
        return false;
    }
    
    // 停止测试
    MotionControl_SendCommand(MOTION_CMD_STOP);
    
    return true;
}

/**
 * @brief 视觉数据测试
 */
bool SystemTest_VisionData(void) {
    DebugMonitor_LogDebug("Testing vision data processing...");
    
    // 创建测试矩形数据包
    uint8_t test_packet[39];
    test_packet[0] = 0xAA;  // 包头
    test_packet[1] = 39;    // 长度
    test_packet[2] = 0x03;  // 矩形命令
    
    // 填充测试矩形坐标
    Point_t test_points[4] = {
        {5.0f, 5.0f},
        {25.0f, 5.0f},
        {25.0f, 15.0f},
        {5.0f, 15.0f}
    };
    
    for (int i = 0; i < 4; i++) {
        memcpy(&test_packet[3 + i * 8], &test_points[i].x, 4);
        memcpy(&test_packet[3 + i * 8 + 4], &test_points[i].y, 4);
    }
    
    // 置信度
    float confidence = 0.9f;
    memcpy(&test_packet[35], &confidence, 4);
    
    // 校验和
    test_packet[38] = VisionData_CalculateChecksum(test_packet, 38);
    
    // 处理数据包
    bool result = VisionData_ProcessPacket(test_packet, 39);
    if (!result) {
        DebugMonitor_LogError("Vision data processing failed");
        return false;
    }
    
    // 检查矩形数据是否有效
    if (!VisionData_IsRectangleValid()) {
        DebugMonitor_LogError("Vision rectangle data invalid");
        return false;
    }
    
    return true;
}

/**
 * @brief 等待操作完成
 */
bool SystemTest_WaitForCompletion(uint32_t timeout_ms) {
    uint32_t start_time = HAL_GetTick();
    
    while (HAL_GetTick() - start_time < timeout_ms) {
        if (MotionControl_IsCompleted() || MotionControl_IsIdle()) {
            return true;
        }
        osDelay(100);
    }
    
    return false;
}

/**
 * @brief 打印测试结果
 */
void SystemTest_PrintResults(void) {
    DebugMonitor_LogInfo("=== Test Results ===");
    DebugMonitor_LogInfo("Total Tests: %d", test_suite.test_count);
    DebugMonitor_LogInfo("Passed: %d", test_suite.passed_count);
    DebugMonitor_LogInfo("Failed: %d", test_suite.test_count - test_suite.passed_count);
    DebugMonitor_LogInfo("Success Rate: %.1f%%", 
                        test_suite.test_count > 0 ? 
                        (float)test_suite.passed_count * 100 / test_suite.test_count : 0);
    DebugMonitor_LogInfo("Total Duration: %.2fs", test_suite.total_duration / 1000.0f);
    
    // 详细结果
    for (int i = 0; i < test_suite.test_count; i++) {
        TestResult_t *result = &test_suite.results[i];
        DebugMonitor_LogInfo("%d. %s: %s (%.2fs)", 
                            i + 1,
                            result->description,
                            result->passed ? "PASS" : "FAIL",
                            result->duration_ms / 1000.0f);
    }
}

/**
 * @brief 演示复位功能
 */
void SystemTest_Demo_Reset(void) {
    DebugMonitor_LogInfo("=== Demo: Reset Function ===");
    MotionControl_SendCommand(MOTION_CMD_RESET);
}

/**
 * @brief 演示边线移动
 */
void SystemTest_Demo_BorderMovement(void) {
    DebugMonitor_LogInfo("=== Demo: Border Movement ===");
    MotionControl_SendCommand(MOTION_CMD_START_BORDER);
}

/**
 * @brief 演示所有功能
 */
void SystemTest_Demo_All(void) {
    DebugMonitor_LogInfo("=== Demo: All Functions ===");
    
    // 1. 复位
    DebugMonitor_LogInfo("1. Reset to origin...");
    MotionControl_SendCommand(MOTION_CMD_RESET);
    SystemTest_WaitForCompletion(5000);
    
    osDelay(2000);
    
    // 2. 边线移动
    DebugMonitor_LogInfo("2. Border movement...");
    MotionControl_SendCommand(MOTION_CMD_START_BORDER);
    osDelay(10000);  // 运行10秒
    MotionControl_SendCommand(MOTION_CMD_STOP);
    
    osDelay(2000);
    
    // 3. 再次复位
    DebugMonitor_LogInfo("3. Reset again...");
    MotionControl_SendCommand(MOTION_CMD_RESET);
    SystemTest_WaitForCompletion(5000);
    
    DebugMonitor_LogInfo("Demo completed!");
}
