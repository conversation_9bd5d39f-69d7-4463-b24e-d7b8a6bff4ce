#ifndef __SYSTEM_TEST_H_
#define __SYSTEM_TEST_H_

#include "sys.h"

// 测试类型枚举
typedef enum {
    TEST_TYPE_SERVO_BASIC,          // 基本舵机测试
    TEST_TYPE_COORDINATE_CONVERT,   // 坐标转换测试
    TEST_TYPE_PATH_GENERATION,      // 路径生成测试
    TEST_TYPE_RESET_FUNCTION,       // 复位功能测试
    TEST_TYPE_BORDER_MOVEMENT,      // 边线移动测试
    TEST_TYPE_TAPE_TRACKING,        // 胶带跟踪测试
    TEST_TYPE_VISION_DATA,          // 视觉数据测试
    TEST_TYPE_FULL_SYSTEM          // 完整系统测试
} TestType_t;

// 测试结果结构体
typedef struct {
    TestType_t test_type;
    bool passed;
    uint32_t duration_ms;
    float max_error;
    char description[64];
} TestResult_t;

// 测试套件结构体
typedef struct {
    TestResult_t results[10];
    uint8_t test_count;
    uint8_t passed_count;
    uint32_t total_duration;
} TestSuite_t;

// 全局变量声明
extern TestSuite_t test_suite;

// 测试初始化和控制
void SystemTest_Init(void);
void SystemTest_RunAll(void);
void SystemTest_RunSingle(TestType_t test_type);
void SystemTest_PrintResults(void);

// 具体测试函数
bool SystemTest_ServoBasic(void);
bool SystemTest_CoordinateConvert(void);
bool SystemTest_PathGeneration(void);
bool SystemTest_ResetFunction(void);
bool SystemTest_BorderMovement(void);
bool SystemTest_TapeTracking(void);
bool SystemTest_VisionData(void);
bool SystemTest_FullSystem(void);

// 测试辅助函数
void SystemTest_CreateTestRectangle(Point_t points[4]);
bool SystemTest_WaitForCompletion(uint32_t timeout_ms);
float SystemTest_MeasurePositionAccuracy(Point_t expected, uint32_t duration_ms);

// 演示功能
void SystemTest_Demo_Reset(void);
void SystemTest_Demo_BorderMovement(void);
void SystemTest_Demo_TapeTracking(void);
void SystemTest_Demo_All(void);

#endif // __SYSTEM_TEST_H_
