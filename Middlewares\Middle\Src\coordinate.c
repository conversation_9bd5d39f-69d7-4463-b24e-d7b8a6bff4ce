#include "coordinate.h"

// 全局变量定义
Screen_t screen_config = {
    .width = 30.0f,           // 假设屏幕宽度30cm
    .height = 20.0f,          // 假设屏幕高度20cm
    .center = {15.0f, 10.0f}, // 屏幕中心
    .origin = {15.0f, 10.0f}  // 原点位置(初始为中心)
};

Point_t current_position = {15.0f, 10.0f}; // 当前位置
Point_t target_position = {15.0f, 10.0f};  // 目标位置

// 舵机角度范围和映射参数
#define SERVO_X_MIN_ANGLE 5.0f
#define SERVO_X_MAX_ANGLE 50.0f
#define SERVO_Y_MIN_ANGLE 0.0f
#define SERVO_Y_MAX_ANGLE 50.0f

// 屏幕坐标到舵机角度的映射范围
#define SCREEN_X_MIN 0.0f
#define SCREEN_X_MAX 30.0f
#define SCREEN_Y_MIN 0.0f
#define SCREEN_Y_MAX 20.0f

/**
 * @brief 将屏幕坐标转换为舵机角度
 * @param point 屏幕坐标点(cm)
 * @return 舵机角度
 */
ServoAngle_t Point_To_ServoAngle(Point_t point)
{
    ServoAngle_t angle;

    // X轴映射：屏幕坐标 -> 下舵机角度
    float x_ratio = (point.x - SCREEN_X_MIN) / (SCREEN_X_MAX - SCREEN_X_MIN);
    x_ratio = fmaxf(0.0f, fminf(1.0f, x_ratio)); // 限制在[0,1]
    angle.x_angle = SERVO_X_MIN_ANGLE + x_ratio * (SERVO_X_MAX_ANGLE - SERVO_X_MIN_ANGLE);

    // Y轴映射：屏幕坐标 -> 上舵机角度
    float y_ratio = (point.y - SCREEN_Y_MIN) / (SCREEN_Y_MAX - SCREEN_Y_MIN);
    y_ratio = fmaxf(0.0f, fminf(1.0f, y_ratio)); // 限制在[0,1]
    angle.y_angle = SERVO_Y_MIN_ANGLE + y_ratio * (SERVO_Y_MAX_ANGLE - SERVO_Y_MIN_ANGLE);

    return angle;
}

/**
 * @brief 将舵机角度转换为屏幕坐标
 * @param angle 舵机角度
 * @return 屏幕坐标点(cm)
 */
Point_t ServoAngle_To_Point(ServoAngle_t angle)
{
    Point_t point;

    // X轴反向映射：下舵机角度 -> 屏幕坐标
    float x_ratio = (angle.x_angle - SERVO_X_MIN_ANGLE) / (SERVO_X_MAX_ANGLE - SERVO_X_MIN_ANGLE);
    point.x = SCREEN_X_MIN + x_ratio * (SCREEN_X_MAX - SCREEN_X_MIN);

    // Y轴反向映射：上舵机角度 -> 屏幕坐标
    float y_ratio = (angle.y_angle - SERVO_Y_MIN_ANGLE) / (SERVO_Y_MAX_ANGLE - SERVO_Y_MIN_ANGLE);
    point.y = SCREEN_Y_MIN + y_ratio * (SCREEN_Y_MAX - SCREEN_Y_MIN);

    return point;
}

/**
 * @brief 计算两点间距离
 */
float Point_Distance(Point_t p1, Point_t p2)
{
    float dx = p2.x - p1.x;
    float dy = p2.y - p1.y;
    return sqrtf(dx * dx + dy * dy);
}

/**
 * @brief 计算两点间角度(弧度)
 */
float Point_Angle(Point_t p1, Point_t p2)
{
    return atan2f(p2.y - p1.y, p2.x - p1.x);
}

/**
 * @brief 绕中心点旋转
 */
Point_t Point_Rotate(Point_t point, Point_t center, float angle)
{
    Point_t result;
    float cos_a = cosf(angle);
    float sin_a = sinf(angle);

    float dx = point.x - center.x;
    float dy = point.y - center.y;

    result.x = center.x + dx * cos_a - dy * sin_a;
    result.y = center.y + dx * sin_a + dy * cos_a;

    return result;
}

/**
 * @brief 两点间线性插值
 */
Point_t Point_Interpolate(Point_t p1, Point_t p2, float ratio)
{
    Point_t result;
    result.x = p1.x + ratio * (p2.x - p1.x);
    result.y = p1.y + ratio * (p2.y - p1.y);
    return result;
}

/**
 * @brief 从四个顶点创建矩形
 */
Rectangle_t Rectangle_Create(Point_t points[4])
{
    Rectangle_t rect;

    // 复制顶点
    for (int i = 0; i < 4; i++)
    {
        rect.points[i] = points[i];
    }

    // 计算中心点
    rect.center.x = (points[0].x + points[1].x + points[2].x + points[3].x) / 4.0f;
    rect.center.y = (points[0].y + points[1].y + points[2].y + points[3].y) / 4.0f;

    // 计算宽度和高度(简化处理，假设是轴对齐的矩形)
    rect.width = Point_Distance(points[0], points[1]);
    rect.height = Point_Distance(points[1], points[2]);

    // 计算旋转角度
    rect.angle = Point_Angle(points[0], points[1]);

    return rect;
}

/**
 * @brief 从中心点、尺寸和角度创建矩形
 */
Rectangle_t Rectangle_CreateFromCenter(Point_t center, float width, float height, float angle)
{
    Rectangle_t rect;
    rect.center = center;
    rect.width = width;
    rect.height = height;
    rect.angle = angle;

    // 计算四个顶点(先计算未旋转的矩形，再旋转)
    Point_t corners[4] = {
        {-width / 2, -height / 2}, // 左下
        {width / 2, -height / 2},  // 右下
        {width / 2, height / 2},   // 右上
        {-width / 2, height / 2}   // 左上
    };

    // 旋转并平移到中心点
    for (int i = 0; i < 4; i++)
    {
        Point_t rotated = Point_Rotate(corners[i], (Point_t){0, 0}, angle);
        rect.points[i].x = center.x + rotated.x;
        rect.points[i].y = center.y + rotated.y;
    }

    return rect;
}

/**
 * @brief 清空路径
 */
void Path_Clear(Path_t *path)
{
    path->count = 0;
    path->total_time_ms = 0;
}

/**
 * @brief 添加路径点
 */
void Path_AddPoint(Path_t *path, Point_t point, uint32_t time_ms)
{
    if (path->count < 200)
    {
        path->points[path->count].position = point;
        path->points[path->count].time_ms = time_ms;
        path->count++;
        if (time_ms > path->total_time_ms)
        {
            path->total_time_ms = time_ms;
        }
    }
}

/**
 * @brief 生成矩形路径
 */
void Path_GenerateRectangle(Path_t *path, Rectangle_t rect, uint32_t total_time_ms, bool clockwise)
{
    Path_Clear(path);

    // 确定顶点顺序
    int order[4];
    if (clockwise)
    {
        order[0] = 0;
        order[1] = 1;
        order[2] = 2;
        order[3] = 3;
    }
    else
    {
        order[0] = 0;
        order[1] = 3;
        order[2] = 2;
        order[3] = 1;
    }

    // 计算每条边的长度和时间分配
    float total_length = 0;
    float edge_lengths[4];
    for (int i = 0; i < 4; i++)
    {
        int next = (i + 1) % 4;
        edge_lengths[i] = Point_Distance(rect.points[order[i]], rect.points[order[next]]);
        total_length += edge_lengths[i];
    }

    uint32_t current_time = 0;

    // 为每条边生成路径点
    for (int edge = 0; edge < 4; edge++)
    {
        int start_idx = order[edge];
        int end_idx = order[(edge + 1) % 4];

        uint32_t edge_time = (uint32_t)(total_time_ms * edge_lengths[edge] / total_length);
        int points_per_edge = 10; // 每条边10个点

        for (int i = 0; i <= points_per_edge; i++)
        {
            float ratio = (float)i / points_per_edge;
            Point_t point = Point_Interpolate(rect.points[start_idx], rect.points[end_idx], ratio);
            uint32_t point_time = current_time + (uint32_t)(edge_time * ratio);
            Path_AddPoint(path, point, point_time);
        }

        current_time += edge_time;
    }
}

/**
 * @brief 系统初始化
 */
void Coordinate_Init(void)
{
    // 初始化屏幕配置
    // 这些参数需要根据实际硬件调整
    screen_config.width = 30.0f;
    screen_config.height = 20.0f;
    screen_config.center = (Point_t){15.0f, 10.0f};
    screen_config.origin = (Point_t){15.0f, 10.0f};

    // 初始化当前位置为原点
    current_position = screen_config.origin;
    target_position = screen_config.origin;
}

/**
 * @brief 生成屏幕边线路径
 */
void Path_GenerateScreenBorder(Path_t *path, uint32_t total_time_ms, bool clockwise)
{
    Path_Clear(path);

    // 屏幕边界矩形(留2cm边距)
    float margin = 2.0f;
    Rectangle_t border = Rectangle_CreateFromCenter(
        screen_config.center,
        screen_config.width - 2 * margin,
        screen_config.height - 2 * margin,
        0.0f);

    Path_GenerateRectangle(path, border, total_time_ms, clockwise);
}

/**
 * @brief 生成回到原点的路径
 */
void Path_GenerateReturnToOrigin(Path_t *path, Point_t start, uint32_t total_time_ms)
{
    Path_Clear(path);

    // 简单直线路径
    int num_points = 20;
    for (int i = 0; i <= num_points; i++)
    {
        float ratio = (float)i / num_points;
        Point_t point = Point_Interpolate(start, screen_config.origin, ratio);
        uint32_t time = (uint32_t)(total_time_ms * ratio);
        Path_AddPoint(path, point, time);
    }
}

/**
 * @brief 获取当前时间的目标位置
 */
PathPoint_t Path_GetCurrentTarget(Path_t *path, uint32_t current_time_ms)
{
    PathPoint_t result = {{0, 0}, 0};

    if (path->count == 0)
    {
        return result;
    }

    // 如果时间超过路径总时间，返回最后一个点
    if (current_time_ms >= path->total_time_ms)
    {
        return path->points[path->count - 1];
    }

    // 查找当前时间对应的路径段
    for (uint16_t i = 0; i < path->count - 1; i++)
    {
        if (current_time_ms >= path->points[i].time_ms &&
            current_time_ms <= path->points[i + 1].time_ms)
        {

            // 在两个路径点之间插值
            uint32_t dt = path->points[i + 1].time_ms - path->points[i].time_ms;
            if (dt > 0)
            {
                float ratio = (float)(current_time_ms - path->points[i].time_ms) / dt;
                result.position = Point_Interpolate(
                    path->points[i].position,
                    path->points[i + 1].position,
                    ratio);
                result.time_ms = current_time_ms;
            }
            else
            {
                result = path->points[i];
            }
            break;
        }
    }

    return result;
}

/**
 * @brief 检查路径是否完成
 */
bool Path_IsCompleted(Path_t *path, uint32_t current_time_ms)
{
    return current_time_ms >= path->total_time_ms;
}

/**
 * @brief 计算点到矩形边缘的距离
 */
float Rectangle_DistanceToEdge(Rectangle_t rect, Point_t point)
{
    float min_distance = INFINITY;

    // 计算到每条边的距离
    for (int i = 0; i < 4; i++)
    {
        int next = (i + 1) % 4;
        Point_t p1 = rect.points[i];
        Point_t p2 = rect.points[next];

        // 计算点到线段的距离
        float A = point.x - p1.x;
        float B = point.y - p1.y;
        float C = p2.x - p1.x;
        float D = p2.y - p1.y;

        float dot = A * C + B * D;
        float len_sq = C * C + D * D;

        float param = -1;
        if (len_sq != 0)
        {
            param = dot / len_sq;
        }

        float xx, yy;
        if (param < 0)
        {
            xx = p1.x;
            yy = p1.y;
        }
        else if (param > 1)
        {
            xx = p2.x;
            yy = p2.y;
        }
        else
        {
            xx = p1.x + param * C;
            yy = p1.y + param * D;
        }

        float dx = point.x - xx;
        float dy = point.y - yy;
        float distance = sqrtf(dx * dx + dy * dy);

        if (distance < min_distance)
        {
            min_distance = distance;
        }
    }

    return min_distance;
}

/**
 * @brief 检查点是否在矩形内部
 */
bool Rectangle_IsPointInside(Rectangle_t rect, Point_t point)
{
    // 使用射线法检测点是否在多边形内部
    int intersections = 0;

    for (int i = 0; i < 4; i++)
    {
        int next = (i + 1) % 4;
        Point_t p1 = rect.points[i];
        Point_t p2 = rect.points[next];

        if (((p1.y > point.y) != (p2.y > point.y)) &&
            (point.x < (p2.x - p1.x) * (point.y - p1.y) / (p2.y - p1.y) + p1.x))
        {
            intersections++;
        }
    }

    return (intersections % 2) == 1;
}

/**
 * @brief 设置屏幕配置
 */
void Screen_SetConfig(float width, float height, Point_t center, Point_t origin)
{
    screen_config.width = width;
    screen_config.height = height;
    screen_config.center = center;
    screen_config.origin = origin;
}
