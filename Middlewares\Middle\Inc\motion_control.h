#ifndef __MOTION_CONTROL_H_
#define __MOTION_CONTROL_H_

#include "sys.h"
#include "coordinate.h"

// 运动控制状态枚举
typedef enum {
    MOTION_STATE_IDLE,              // 空闲状态
    MOTION_STATE_RESET,             // 复位状态
    MOTION_STATE_SCREEN_BORDER,     // 屏幕边线移动
    MOTION_STATE_TAPE_TRACKING,     // 胶带跟踪
    MOTION_STATE_ERROR              // 错误状态
} MotionState_t;

// 运动控制命令枚举
typedef enum {
    MOTION_CMD_NONE,                // 无命令
    MOTION_CMD_RESET,               // 复位命令
    MOTION_CMD_START_BORDER,        // 开始边线移动
    MOTION_CMD_START_TAPE,          // 开始胶带跟踪
    MOTION_CMD_STOP,                // 停止命令
    MOTION_CMD_EMERGENCY_STOP       // 紧急停止
} MotionCommand_t;

// 运动控制参数结构体
typedef struct {
    uint32_t reset_time_ms;         // 复位时间(毫秒)
    uint32_t border_time_ms;        // 边线移动时间(毫秒)
    uint32_t tape_time_ms;          // 胶带跟踪时间(毫秒)
    float position_tolerance;       // 位置容差(cm)
    float edge_tolerance;           // 边缘容差(cm)
    bool clockwise;                 // 是否顺时针
} MotionParams_t;

// 运动控制状态结构体
typedef struct {
    MotionState_t current_state;    // 当前状态
    MotionState_t previous_state;   // 前一个状态
    MotionCommand_t pending_command; // 待处理命令
    
    Path_t current_path;            // 当前路径
    Rectangle_t target_rectangle;   // 目标矩形(用于胶带跟踪)
    
    uint32_t state_start_time;      // 状态开始时间
    uint32_t last_update_time;      // 上次更新时间
    
    Point_t current_position;       // 当前位置
    Point_t target_position;        // 目标位置
    
    bool path_completed;            // 路径是否完成
    bool emergency_stop;            // 紧急停止标志
    
    // 统计信息
    uint32_t total_runtime;         // 总运行时间
    float max_deviation;            // 最大偏差
    uint32_t off_track_count;       // 脱轨次数
    uint32_t off_track_distance;    // 脱轨距离(cm)
} MotionControl_t;

// 胶带跟踪状态结构体
typedef struct {
    Rectangle_t tape_rectangle;     // 胶带矩形
    bool is_valid;                  // 矩形是否有效
    uint32_t last_update_time;      // 上次更新时间
    float off_track_distance;       // 脱轨距离
    uint32_t off_track_start_time;  // 脱轨开始时间
    bool is_off_track;              // 是否脱轨
    int score_deduction;            // 扣分
} TapeTracking_t;

// 全局变量声明
extern MotionControl_t motion_control;
extern MotionParams_t motion_params;
extern TapeTracking_t tape_tracking;

// 运动控制初始化和配置
void MotionControl_Init(void);
void MotionControl_SetParams(MotionParams_t params);
void MotionControl_Reset(void);

// 状态机控制
void MotionControl_Update(void);
void MotionControl_SendCommand(MotionCommand_t command);
MotionState_t MotionControl_GetState(void);
bool MotionControl_IsIdle(void);
bool MotionControl_IsCompleted(void);

// 路径和位置控制
void MotionControl_SetTargetPosition(Point_t position);
Point_t MotionControl_GetCurrentPosition(void);
Point_t MotionControl_GetTargetPosition(void);
void MotionControl_UpdatePosition(void);

// 胶带跟踪功能
void MotionControl_SetTapeRectangle(Point_t points[4]);
void MotionControl_UpdateTapeTracking(void);
bool MotionControl_IsOnTape(Point_t position);
float MotionControl_GetDistanceToTape(Point_t position);

// 状态查询和统计
uint32_t MotionControl_GetRuntime(void);
float MotionControl_GetMaxDeviation(void);
int MotionControl_GetScore(void);
void MotionControl_GetStatistics(uint32_t *runtime, float *max_dev, int *score);

// 错误处理
void MotionControl_HandleError(const char* error_msg);
void MotionControl_ClearError(void);
bool MotionControl_HasError(void);

// 调试和监控
void MotionControl_PrintStatus(void);
void MotionControl_PrintStatistics(void);

#endif // __MOTION_CONTROL_H_
