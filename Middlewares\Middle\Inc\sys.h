#ifndef _SYS_H_
#define _SYS_H_

// ??????????
// ??????????????G??]?????

#include <stdio.h>
#include <stdlib.h> //atof
#include <string.h>

#include "cmsis_os.h"
#include "dma.h"
#include "duoji.h"
#include "gpio.h"
#include "main.h"
#include "others.h"
#include "ringbuf.h"
#include "tim.h"
#include "usart.h"
#include "coordinate.h"
#include "motion_control.h"
#include "vision_data.h"
#include "debug_monitor.h"
#include "system_test.h"

#endif // _SYS_H_
