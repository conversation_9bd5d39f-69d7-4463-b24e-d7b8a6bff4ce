# 舵机运动控制系统

## 项目概述

本项目是一个基于STM32F407的舵机运动控制系统，用于控制红色光斑在屏幕上的精确移动。系统支持复位、边线移动、胶带跟踪等多种运动模式，满足电子设计竞赛的技术要求。

## 功能特性

### 基本要求实现

1. **复位功能**：红色光斑从任意位置回到原点，误差≤2cm
2. **边线移动**：30秒内沿屏幕四周边线顺时针移动一周，距边线≤2cm
3. **A4胶带跟踪**：30秒内沿胶带顺时针移动，支持脱轨检测和扣分逻辑
4. **旋转胶带跟踪**：处理任意角度的A4胶带跟踪

### 系统架构

- **坐标系统模块**：屏幕坐标与舵机角度转换、路径规划
- **运动控制状态机**：管理不同运动模式的切换和执行
- **视觉数据接收**：UART通信协议，接收矩形坐标数据
- **调试监控系统**：实时状态监控、错误处理、性能统计
- **系统测试模块**：自动化测试和演示功能

## 硬件配置

- **主控**：STM32F407
- **舵机**：2个舵机（X轴和Y轴）
- **PWM控制**：TIM8 CH3/CH4
- **通信**：UART1 (115200波特率)
- **操作系统**：FreeRTOS

## 软件架构

```
├── Core/
│   └── Src/
│       └── freertos.c          # 主任务和系统初始化
├── Middlewares/Middle/
│   ├── Inc/
│   │   ├── coordinate.h        # 坐标系统
│   │   ├── motion_control.h    # 运动控制
│   │   ├── vision_data.h       # 视觉数据处理
│   │   ├── debug_monitor.h     # 调试监控
│   │   ├── system_test.h       # 系统测试
│   │   └── duoji.h            # 舵机控制
│   └── Src/
│       ├── coordinate.c
│       ├── motion_control.c
│       ├── vision_data.c
│       ├── debug_monitor.c
│       ├── system_test.c
│       ├── duoji.c
│       ├── ringbuf.c          # 环形缓冲区
│       └── others.c           # 工具函数
```

## 通信协议

### 视觉数据包格式

```
[包头][长度][命令][数据][校验和]
```

#### 命令类型
- `0x01`: X轴舵机控制
- `0x02`: Y轴舵机控制  
- `0x03`: 矩形坐标数据
- `0x04`: 复位命令
- `0x05`: 开始边线移动
- `0x06`: 开始胶带跟踪
- `0x07`: 停止命令
- `0x08`: 状态查询

#### 矩形数据包 (39字节)
```
[0xAA][39][0x03][x1][y1][x2][y2][x3][y3][x4][y4][置信度][校验和]
```

## 使用方法

### 1. 系统初始化
系统启动后会自动初始化所有模块，舵机移动到初始位置。

### 2. 复位功能
```c
MotionControl_SendCommand(MOTION_CMD_RESET);
```
或发送UART命令：`[0xAA][4][0x04][校验和]`

### 3. 边线移动
```c
MotionControl_SendCommand(MOTION_CMD_START_BORDER);
```
或发送UART命令：`[0xAA][4][0x05][校验和]`

### 4. 胶带跟踪
首先发送矩形坐标数据，然后启动跟踪：
```c
MotionControl_SendCommand(MOTION_CMD_START_TAPE);
```

### 5. 系统测试
```c
SystemTest_RunAll();        // 运行所有测试
SystemTest_Demo_All();      // 运行演示程序
```

## 参数配置

### 屏幕参数
```c
screen_config.width = 30.0f;    // 屏幕宽度(cm)
screen_config.height = 20.0f;   // 屏幕高度(cm)
```

### 舵机角度范围
```c
#define SERVO_X_MIN_ANGLE 5.0f
#define SERVO_X_MAX_ANGLE 50.0f
#define SERVO_Y_MIN_ANGLE 0.0f
#define SERVO_Y_MAX_ANGLE 50.0f
```

### 运动参数
```c
motion_params.reset_time_ms = 3000;      // 复位时间
motion_params.border_time_ms = 30000;    // 边线移动时间
motion_params.tape_time_ms = 30000;      // 胶带跟踪时间
motion_params.position_tolerance = 2.0f; // 位置容差
motion_params.edge_tolerance = 2.0f;     // 边缘容差
```

## 调试功能

### 日志输出
```c
DebugMonitor_LogInfo("信息日志");
DebugMonitor_LogWarning("警告日志");
DebugMonitor_LogError("错误日志");
```

### 状态监控
```c
DebugMonitor_PrintStatus();      // 打印系统状态
DebugMonitor_PrintStatistics();  // 打印统计信息
```

### 实时监控
```c
DebugMonitor_StartRealTimeMonitor();  // 开始实时监控
DebugMonitor_StopRealTimeMonitor();   // 停止实时监控
```

## 性能指标

- **位置精度**：≤2cm
- **响应时间**：<100ms
- **边线移动时间**：30秒
- **胶带跟踪时间**：30秒
- **复位时间**：3秒

## 故障排除

### 常见问题

1. **舵机不响应**
   - 检查PWM信号连接
   - 确认舵机电源供应
   - 检查角度范围设置

2. **位置精度不够**
   - 校准坐标转换参数
   - 调整舵机机械安装
   - 优化平滑运动参数

3. **视觉数据接收失败**
   - 检查UART连接和波特率
   - 验证数据包格式
   - 检查校验和计算

4. **路径跟踪偏差大**
   - 调整路径插值点数
   - 优化运动控制算法
   - 检查时间同步

## 开发说明

### 添加新功能
1. 在相应的头文件中声明函数
2. 在实现文件中添加功能代码
3. 在系统测试中添加测试用例
4. 更新文档说明

### 调试技巧
1. 使用调试监控系统查看实时状态
2. 运行系统测试验证功能
3. 分析日志输出定位问题
4. 使用示波器检查PWM信号

## 版本信息

- **版本**：v1.0
- **开发环境**：STM32CubeIDE
- **编译器**：ARM GCC
- **操作系统**：FreeRTOS

## 联系信息

如有问题或建议，请联系开发团队。
