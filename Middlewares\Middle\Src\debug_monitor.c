#include "debug_monitor.h"
#include <stdarg.h>

// 全局变量定义
SystemStatus_t system_status;
DebugLevel_t debug_level = DEBUG_LEVEL_INFO;
static bool real_time_monitor_active = false;

/**
 * @brief 调试监控初始化
 */
void DebugMonitor_Init(void) {
    memset(&system_status, 0, sizeof(SystemStatus_t));
    system_status.system_uptime = HAL_GetTick();
    system_status.last_update_time = system_status.system_uptime;
    
    DebugMonitor_LogInfo("Debug Monitor Initialized");
}

/**
 * @brief 设置调试级别
 */
void DebugMonitor_SetLevel(DebugLevel_t level) {
    debug_level = level;
    DebugMonitor_LogInfo("Debug level set to %d", level);
}

/**
 * @brief 更新系统状态
 */
void DebugMonitor_Update(void) {
    uint32_t current_time = HAL_GetTick();
    system_status.system_uptime = current_time;
    
    // 更新各个子系统状态
    DebugMonitor_UpdateServoStatus();
    DebugMonitor_UpdatePositionStatus();
    DebugMonitor_UpdateMotionStatus();
    DebugMonitor_UpdateVisionStatus();
    
    system_status.last_update_time = current_time;
    
    // 实时监控输出
    if (real_time_monitor_active) {
        DebugMonitor_PrintStatus();
    }
}

/**
 * @brief 更新舵机状态
 */
void DebugMonitor_UpdateServoStatus(void) {
    system_status.servo_x_current = servo_down.current;
    system_status.servo_y_current = servo_up.current;
    system_status.servo_x_target = servo_down.target;
    system_status.servo_y_target = servo_up.target;
}

/**
 * @brief 更新位置状态
 */
void DebugMonitor_UpdatePositionStatus(void) {
    system_status.current_position = MotionControl_GetCurrentPosition();
    system_status.target_position = MotionControl_GetTargetPosition();
    
    // 计算位置误差
    system_status.position_error = Point_Distance(
        system_status.current_position, 
        system_status.target_position
    );
    
    // 记录最大误差
    if (system_status.position_error > system_status.max_position_error) {
        system_status.max_position_error = system_status.position_error;
    }
}

/**
 * @brief 更新运动控制状态
 */
void DebugMonitor_UpdateMotionStatus(void) {
    system_status.motion_state = MotionControl_GetState();
    system_status.path_active = !MotionControl_IsIdle();
    
    // 计算状态持续时间
    static MotionState_t last_state = MOTION_STATE_IDLE;
    static uint32_t state_start_time = 0;
    
    if (system_status.motion_state != last_state) {
        last_state = system_status.motion_state;
        state_start_time = HAL_GetTick();
    }
    
    system_status.state_duration = HAL_GetTick() - state_start_time;
}

/**
 * @brief 更新视觉数据状态
 */
void DebugMonitor_UpdateVisionStatus(void) {
    system_status.vision_connected = VisionData_IsConnected();
    system_status.vision_packet_count = VisionData_GetPacketCount();
    system_status.vision_error_count = VisionData_GetErrorCount();
    system_status.rectangle_valid = VisionData_IsRectangleValid();
}

/**
 * @brief 日志输出函数
 */
void DebugMonitor_Log(DebugLevel_t level, const char* format, ...) {
    if (level > debug_level) {
        return;
    }
    
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // 添加时间戳和级别前缀
    const char* level_str[] = {"", "ERR", "WAR", "INF", "DBG", "VRB"};
    char output[300];
    snprintf(output, sizeof(output), "[%lu][%s] %s\r\n", 
             HAL_GetTick(), level_str[level], buffer);
    
    // 通过UART输出
    HAL_UART_Transmit(&huart1, (uint8_t*)output, strlen(output), 100);
}

/**
 * @brief 错误日志
 */
void DebugMonitor_LogError(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[256];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    DebugMonitor_Log(DEBUG_LEVEL_ERROR, buffer);
    DebugMonitor_RecordError(buffer);
}

/**
 * @brief 警告日志
 */
void DebugMonitor_LogWarning(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[256];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    DebugMonitor_Log(DEBUG_LEVEL_WARNING, buffer);
    DebugMonitor_RecordWarning(buffer);
}

/**
 * @brief 信息日志
 */
void DebugMonitor_LogInfo(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[256];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    DebugMonitor_Log(DEBUG_LEVEL_INFO, buffer);
}

/**
 * @brief 调试日志
 */
void DebugMonitor_LogDebug(const char* format, ...) {
    va_list args;
    va_start(args, format);
    char buffer[256];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    DebugMonitor_Log(DEBUG_LEVEL_DEBUG, buffer);
}

/**
 * @brief 获取系统状态
 */
SystemStatus_t DebugMonitor_GetStatus(void) {
    return system_status;
}

/**
 * @brief 打印系统状态
 */
void DebugMonitor_PrintStatus(void) {
    DebugMonitor_LogInfo("=== System Status ===");
    DebugMonitor_LogInfo("Uptime: %lu ms", system_status.system_uptime);
    DebugMonitor_LogInfo("Motion State: %d", system_status.motion_state);
    DebugMonitor_LogInfo("Position: (%.2f, %.2f)", 
                        system_status.current_position.x, 
                        system_status.current_position.y);
    DebugMonitor_LogInfo("Target: (%.2f, %.2f)", 
                        system_status.target_position.x, 
                        system_status.target_position.y);
    DebugMonitor_LogInfo("Position Error: %.2f cm", system_status.position_error);
    DebugMonitor_LogInfo("Vision Connected: %s", 
                        system_status.vision_connected ? "Yes" : "No");
    DebugMonitor_LogInfo("Rectangle Valid: %s", 
                        system_status.rectangle_valid ? "Yes" : "No");
}

/**
 * @brief 打印统计信息
 */
void DebugMonitor_PrintStatistics(void) {
    DebugMonitor_LogInfo("=== Statistics ===");
    DebugMonitor_LogInfo("Max Position Error: %.2f cm", system_status.max_position_error);
    DebugMonitor_LogInfo("Total Commands: %lu", system_status.total_commands);
    DebugMonitor_LogInfo("Successful Commands: %lu", system_status.successful_commands);
    DebugMonitor_LogInfo("Success Rate: %.1f%%", 
                        system_status.total_commands > 0 ? 
                        (float)system_status.successful_commands * 100 / system_status.total_commands : 0);
    DebugMonitor_LogInfo("Vision Packets: %lu", system_status.vision_packet_count);
    DebugMonitor_LogInfo("Vision Errors: %lu", system_status.vision_error_count);
    DebugMonitor_LogInfo("Vision Error Rate: %.1f%%", 
                        VisionData_GetErrorRate() * 100);
    DebugMonitor_LogInfo("System Errors: %lu", system_status.error_count);
    DebugMonitor_LogInfo("System Warnings: %lu", system_status.warning_count);
}

/**
 * @brief 记录命令执行结果
 */
void DebugMonitor_RecordCommand(bool success) {
    system_status.total_commands++;
    if (success) {
        system_status.successful_commands++;
    }
}

/**
 * @brief 记录位置误差
 */
void DebugMonitor_RecordPositionError(float error) {
    if (error > system_status.max_position_error) {
        system_status.max_position_error = error;
    }
}

/**
 * @brief 记录错误
 */
void DebugMonitor_RecordError(const char* error_msg) {
    system_status.error_count++;
    strncpy(system_status.last_error, error_msg, sizeof(system_status.last_error) - 1);
    system_status.last_error[sizeof(system_status.last_error) - 1] = '\0';
}

/**
 * @brief 记录警告
 */
void DebugMonitor_RecordWarning(const char* warning_msg) {
    system_status.warning_count++;
}

/**
 * @brief 开始实时监控
 */
void DebugMonitor_StartRealTimeMonitor(void) {
    real_time_monitor_active = true;
    DebugMonitor_LogInfo("Real-time monitor started");
}

/**
 * @brief 停止实时监控
 */
void DebugMonitor_StopRealTimeMonitor(void) {
    real_time_monitor_active = false;
    DebugMonitor_LogInfo("Real-time monitor stopped");
}

/**
 * @brief 检查实时监控状态
 */
bool DebugMonitor_IsRealTimeActive(void) {
    return real_time_monitor_active;
}

/**
 * @brief 系统健康检查
 */
bool DebugMonitor_CheckSystemHealth(void) {
    bool healthy = true;
    
    // 检查视觉连接
    if (!system_status.vision_connected) {
        DebugMonitor_LogWarning("Vision system disconnected");
        healthy = false;
    }
    
    // 检查位置误差
    if (system_status.position_error > 5.0f) {
        DebugMonitor_LogWarning("Large position error: %.2f cm", system_status.position_error);
        healthy = false;
    }
    
    // 检查错误率
    if (VisionData_GetErrorRate() > 0.1f) {
        DebugMonitor_LogWarning("High vision error rate: %.1f%%", VisionData_GetErrorRate() * 100);
        healthy = false;
    }
    
    return healthy;
}

/**
 * @brief 获取健康评分
 */
uint8_t DebugMonitor_GetHealthScore(void) {
    uint8_t score = 100;
    
    if (!system_status.vision_connected) score -= 20;
    if (system_status.position_error > 2.0f) score -= 15;
    if (VisionData_GetErrorRate() > 0.05f) score -= 10;
    if (system_status.error_count > 0) score -= 5;
    
    return score;
}
