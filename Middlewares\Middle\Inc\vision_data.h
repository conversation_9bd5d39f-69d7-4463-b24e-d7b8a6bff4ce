#ifndef __VISION_DATA_H_
#define __VISION_DATA_H_

#include "sys.h"
#include "coordinate.h"

// 视觉数据命令类型
typedef enum {
    VISION_CMD_NONE = 0x00,             // 无命令
    VISION_CMD_SERVO_X = 0x01,          // X轴舵机控制(原有)
    VISION_CMD_SERVO_Y = 0x02,          // Y轴舵机控制(原有)
    VISION_CMD_RECTANGLE = 0x03,        // 矩形坐标数据
    VISION_CMD_RESET = 0x04,            // 复位命令
    VISION_CMD_START_BORDER = 0x05,     // 开始边线移动
    VISION_CMD_START_TAPE = 0x06,       // 开始胶带跟踪
    VISION_CMD_STOP = 0x07,             // 停止命令
    VISION_CMD_STATUS_REQ = 0x08        // 状态查询
} VisionCommand_t;

// 视觉数据包结构体
typedef struct {
    uint8_t header;                     // 包头 0xAA
    uint8_t length;                     // 数据长度
    uint8_t command;                    // 命令类型
    uint8_t data[32];                   // 数据内容
    uint8_t checksum;                   // 校验和
} VisionPacket_t;

// 矩形数据结构体
typedef struct {
    Point_t points[4];                  // 四个顶点坐标
    uint32_t timestamp;                 // 时间戳
    bool is_valid;                      // 数据是否有效
    float confidence;                   // 置信度
} VisionRectangle_t;

// 视觉数据状态结构体
typedef struct {
    VisionRectangle_t current_rectangle; // 当前矩形数据
    uint32_t last_update_time;          // 上次更新时间
    uint32_t packet_count;              // 接收包计数
    uint32_t error_count;               // 错误包计数
    bool connection_active;             // 连接是否活跃
} VisionData_t;

// 全局变量声明
extern VisionData_t vision_data;

// 视觉数据初始化
void VisionData_Init(void);

// 数据包处理
bool VisionData_ProcessPacket(uint8_t *data, uint8_t length);
bool VisionData_ParseRectangle(uint8_t *data, uint8_t length);
bool VisionData_ValidatePacket(uint8_t *data, uint8_t length);

// 数据获取
VisionRectangle_t VisionData_GetRectangle(void);
bool VisionData_IsRectangleValid(void);
uint32_t VisionData_GetLastUpdateTime(void);

// 状态查询
bool VisionData_IsConnected(void);
uint32_t VisionData_GetPacketCount(void);
uint32_t VisionData_GetErrorCount(void);
float VisionData_GetErrorRate(void);

// 数据发送
void VisionData_SendStatus(void);
void VisionData_SendAck(uint8_t command);
void VisionData_SendError(uint8_t error_code);

// 校验和计算
uint8_t VisionData_CalculateChecksum(uint8_t *data, uint8_t length);

// 调试功能
void VisionData_PrintRectangle(VisionRectangle_t rect);
void VisionData_PrintStatistics(void);

#endif // __VISION_DATA_H_
