#ifndef __DEBUG_MONITOR_H_
#define __DEBUG_MONITOR_H_

#include "sys.h"
#include "coordinate.h"
#include "motion_control.h"
#include "vision_data.h"

// 调试级别
typedef enum {
    DEBUG_LEVEL_NONE = 0,
    DEBUG_LEVEL_ERROR = 1,
    DEBUG_LEVEL_WARNING = 2,
    DEBUG_LEVEL_INFO = 3,
    DEBUG_LEVEL_DEBUG = 4,
    DEBUG_LEVEL_VERBOSE = 5
} DebugLevel_t;

// 系统状态结构体
typedef struct {
    // 系统运行时间
    uint32_t system_uptime;
    uint32_t last_update_time;
    
    // 舵机状态
    float servo_x_current;
    float servo_y_current;
    float servo_x_target;
    float servo_y_target;
    
    // 位置信息
    Point_t current_position;
    Point_t target_position;
    float position_error;
    
    // 运动控制状态
    MotionState_t motion_state;
    uint32_t state_duration;
    bool path_active;
    uint32_t path_progress;
    
    // 视觉数据状态
    bool vision_connected;
    uint32_t vision_packet_count;
    uint32_t vision_error_count;
    bool rectangle_valid;
    
    // 性能统计
    float max_position_error;
    uint32_t total_commands;
    uint32_t successful_commands;
    
    // 错误统计
    uint32_t error_count;
    uint32_t warning_count;
    char last_error[64];
} SystemStatus_t;

// 全局变量声明
extern SystemStatus_t system_status;
extern DebugLevel_t debug_level;

// 调试监控初始化
void DebugMonitor_Init(void);
void DebugMonitor_SetLevel(DebugLevel_t level);

// 系统状态更新
void DebugMonitor_Update(void);
void DebugMonitor_UpdateServoStatus(void);
void DebugMonitor_UpdatePositionStatus(void);
void DebugMonitor_UpdateMotionStatus(void);
void DebugMonitor_UpdateVisionStatus(void);

// 日志输出函数
void DebugMonitor_Log(DebugLevel_t level, const char* format, ...);
void DebugMonitor_LogError(const char* format, ...);
void DebugMonitor_LogWarning(const char* format, ...);
void DebugMonitor_LogInfo(const char* format, ...);
void DebugMonitor_LogDebug(const char* format, ...);

// 状态查询函数
SystemStatus_t DebugMonitor_GetStatus(void);
void DebugMonitor_PrintStatus(void);
void DebugMonitor_PrintStatistics(void);
void DebugMonitor_PrintMotionInfo(void);
void DebugMonitor_PrintVisionInfo(void);

// 性能监控
void DebugMonitor_RecordCommand(bool success);
void DebugMonitor_RecordPositionError(float error);
void DebugMonitor_RecordError(const char* error_msg);
void DebugMonitor_RecordWarning(const char* warning_msg);

// 实时监控
void DebugMonitor_StartRealTimeMonitor(void);
void DebugMonitor_StopRealTimeMonitor(void);
bool DebugMonitor_IsRealTimeActive(void);

// 数据导出
void DebugMonitor_ExportStatus(uint8_t* buffer, uint16_t* length);
void DebugMonitor_ExportStatistics(uint8_t* buffer, uint16_t* length);

// 系统健康检查
bool DebugMonitor_CheckSystemHealth(void);
void DebugMonitor_RunDiagnostics(void);
uint8_t DebugMonitor_GetHealthScore(void);

// 调试命令处理
void DebugMonitor_ProcessCommand(const char* command);
void DebugMonitor_ShowHelp(void);

#endif // __DEBUG_MONITOR_H_
