#include "motion_control.h"

// 全局变量定义
MotionControl_t motion_control;
MotionParams_t motion_params = {
    .reset_time_ms = 3000,      // 3秒复位时间
    .border_time_ms = 30000,    // 30秒边线移动
    .tape_time_ms = 30000,      // 30秒胶带跟踪
    .position_tolerance = 2.0f, // 2cm位置容差
    .edge_tolerance = 2.0f,     // 2cm边缘容差
    .clockwise = true           // 顺时针
};

TapeTracking_t tape_tracking;

// 内部函数声明
static void MotionControl_StateIdle(void);
static void MotionControl_StateReset(void);
static void MotionControl_StateScreenBorder(void);
static void MotionControl_StateTapeTracking(void);
static void MotionControl_StateError(void);
static void MotionControl_ChangeState(MotionState_t new_state);
static uint32_t MotionControl_GetCurrentTime(void);

/**
 * @brief 运动控制初始化
 */
void MotionControl_Init(void)
{
    // 初始化运动控制结构体
    memset(&motion_control, 0, sizeof(MotionControl_t));
    motion_control.current_state = MOTION_STATE_IDLE;
    motion_control.previous_state = MOTION_STATE_IDLE;
    motion_control.pending_command = MOTION_CMD_NONE;

    // 初始化路径
    Path_Clear(&motion_control.current_path);

    // 初始化位置
    motion_control.current_position = screen_config.origin;
    motion_control.target_position = screen_config.origin;

    // 初始化胶带跟踪
    memset(&tape_tracking, 0, sizeof(TapeTracking_t));
    tape_tracking.is_valid = false;

    // 初始化时间
    motion_control.state_start_time = MotionControl_GetCurrentTime();
    motion_control.last_update_time = motion_control.state_start_time;
}

/**
 * @brief 设置运动参数
 */
void MotionControl_SetParams(MotionParams_t params)
{
    motion_params = params;
}

/**
 * @brief 复位运动控制
 */
void MotionControl_Reset(void)
{
    motion_control.emergency_stop = false;
    motion_control.path_completed = false;
    motion_control.total_runtime = 0;
    motion_control.max_deviation = 0;
    motion_control.off_track_count = 0;
    motion_control.off_track_distance = 0;

    MotionControl_ChangeState(MOTION_STATE_IDLE);
}

/**
 * @brief 获取当前时间(毫秒)
 */
static uint32_t MotionControl_GetCurrentTime(void)
{
    return HAL_GetTick();
}

/**
 * @brief 改变状态
 */
static void MotionControl_ChangeState(MotionState_t new_state)
{
    motion_control.previous_state = motion_control.current_state;
    motion_control.current_state = new_state;
    motion_control.state_start_time = MotionControl_GetCurrentTime();
    motion_control.path_completed = false;

    // 清空路径
    Path_Clear(&motion_control.current_path);
}

/**
 * @brief 发送命令
 */
void MotionControl_SendCommand(MotionCommand_t command)
{
    motion_control.pending_command = command;
}

/**
 * @brief 主更新函数
 */
void MotionControl_Update(void)
{
    uint32_t current_time = MotionControl_GetCurrentTime();
    motion_control.last_update_time = current_time;

    // 处理紧急停止
    if (motion_control.emergency_stop)
    {
        MotionControl_ChangeState(MOTION_STATE_ERROR);
        return;
    }

    // 处理待处理的命令
    if (motion_control.pending_command != MOTION_CMD_NONE)
    {
        switch (motion_control.pending_command)
        {
        case MOTION_CMD_RESET:
            MotionControl_ChangeState(MOTION_STATE_RESET);
            break;

        case MOTION_CMD_START_BORDER:
            if (motion_control.current_state == MOTION_STATE_IDLE)
            {
                MotionControl_ChangeState(MOTION_STATE_SCREEN_BORDER);
            }
            break;

        case MOTION_CMD_START_TAPE:
            if (motion_control.current_state == MOTION_STATE_IDLE && tape_tracking.is_valid)
            {
                MotionControl_ChangeState(MOTION_STATE_TAPE_TRACKING);
            }
            break;

        case MOTION_CMD_STOP:
            MotionControl_ChangeState(MOTION_STATE_IDLE);
            break;

        case MOTION_CMD_EMERGENCY_STOP:
            motion_control.emergency_stop = true;
            MotionControl_ChangeState(MOTION_STATE_ERROR);
            break;

        default:
            break;
        }
        motion_control.pending_command = MOTION_CMD_NONE;
    }

    // 执行当前状态的逻辑
    switch (motion_control.current_state)
    {
    case MOTION_STATE_IDLE:
        MotionControl_StateIdle();
        break;

    case MOTION_STATE_RESET:
        MotionControl_StateReset();
        break;

    case MOTION_STATE_SCREEN_BORDER:
        MotionControl_StateScreenBorder();
        break;

    case MOTION_STATE_TAPE_TRACKING:
        MotionControl_StateTapeTracking();
        break;

    case MOTION_STATE_ERROR:
        MotionControl_StateError();
        break;
    }

    // 更新位置控制
    MotionControl_UpdatePosition();
}

/**
 * @brief 空闲状态处理
 */
static void MotionControl_StateIdle(void)
{
    // 在空闲状态下，保持当前位置
    motion_control.target_position = motion_control.current_position;
}

/**
 * @brief 复位状态处理
 */
static void MotionControl_StateReset(void)
{
    uint32_t elapsed_time = MotionControl_GetCurrentTime() - motion_control.state_start_time;

    // 第一次进入复位状态时生成路径
    if (motion_control.current_path.count == 0)
    {
        Path_GenerateReturnToOrigin(&motion_control.current_path,
                                    motion_control.current_position,
                                    motion_params.reset_time_ms);
    }

    // 检查是否完成复位
    if (Path_IsCompleted(&motion_control.current_path, elapsed_time))
    {
        motion_control.current_position = screen_config.origin;
        motion_control.target_position = screen_config.origin;
        motion_control.path_completed = true;
        MotionControl_ChangeState(MOTION_STATE_IDLE);
    }
    else
    {
        // 更新目标位置
        PathPoint_t target = Path_GetCurrentTarget(&motion_control.current_path, elapsed_time);
        motion_control.target_position = target.position;
    }
}

/**
 * @brief 屏幕边线移动状态处理
 */
static void MotionControl_StateScreenBorder(void)
{
    uint32_t elapsed_time = MotionControl_GetCurrentTime() - motion_control.state_start_time;

    // 第一次进入状态时生成路径
    if (motion_control.current_path.count == 0)
    {
        Path_GenerateScreenBorder(&motion_control.current_path,
                                  motion_params.border_time_ms,
                                  motion_params.clockwise);
    }

    // 检查是否完成
    if (Path_IsCompleted(&motion_control.current_path, elapsed_time))
    {
        motion_control.path_completed = true;
        MotionControl_ChangeState(MOTION_STATE_IDLE);
    }
    else
    {
        // 更新目标位置
        PathPoint_t target = Path_GetCurrentTarget(&motion_control.current_path, elapsed_time);
        motion_control.target_position = target.position;

        // 检查是否偏离边线
        float distance_to_edge = motion_params.edge_tolerance + 1.0f; // 简化处理
        if (distance_to_edge > motion_params.edge_tolerance)
        {
            motion_control.max_deviation = fmaxf(motion_control.max_deviation, distance_to_edge);
        }
    }
}

/**
 * @brief 胶带跟踪状态处理
 */
static void MotionControl_StateTapeTracking(void)
{
    uint32_t elapsed_time = MotionControl_GetCurrentTime() - motion_control.state_start_time;

    // 第一次进入状态时生成路径
    if (motion_control.current_path.count == 0 && tape_tracking.is_valid)
    {
        Path_GenerateRectangle(&motion_control.current_path,
                               tape_tracking.tape_rectangle,
                               motion_params.tape_time_ms,
                               motion_params.clockwise);
    }

    // 检查是否完成
    if (Path_IsCompleted(&motion_control.current_path, elapsed_time))
    {
        motion_control.path_completed = true;
        MotionControl_ChangeState(MOTION_STATE_IDLE);
    }
    else
    {
        // 更新目标位置
        PathPoint_t target = Path_GetCurrentTarget(&motion_control.current_path, elapsed_time);
        motion_control.target_position = target.position;

        // 更新胶带跟踪状态
        MotionControl_UpdateTapeTracking();
    }
}

/**
 * @brief 错误状态处理
 */
static void MotionControl_StateError(void)
{
    // 在错误状态下停止运动
    motion_control.target_position = motion_control.current_position;
}

/**
 * @brief 更新位置控制
 */
void MotionControl_UpdatePosition(void)
{
    // 将目标位置转换为舵机角度并设置
    ServoAngle_t target_angle = Point_To_ServoAngle(motion_control.target_position);

    // 设置舵机目标角度
    Servo_Set(1, target_angle.y_angle); // 上舵机(Y轴)
    Servo_Set(2, target_angle.x_angle); // 下舵机(X轴)

    // 更新当前位置(简化处理，假设舵机能够跟上目标)
    motion_control.current_position = motion_control.target_position;
}

/**
 * @brief 获取当前状态
 */
MotionState_t MotionControl_GetState(void)
{
    return motion_control.current_state;
}

/**
 * @brief 检查是否空闲
 */
bool MotionControl_IsIdle(void)
{
    return motion_control.current_state == MOTION_STATE_IDLE;
}

/**
 * @brief 检查是否完成
 */
bool MotionControl_IsCompleted(void)
{
    return motion_control.path_completed;
}

/**
 * @brief 设置胶带矩形
 */
void MotionControl_SetTapeRectangle(Point_t points[4])
{
    tape_tracking.tape_rectangle = Rectangle_Create(points);
    tape_tracking.is_valid = true;
    tape_tracking.last_update_time = MotionControl_GetCurrentTime();
    tape_tracking.off_track_distance = 0;
    tape_tracking.is_off_track = false;
    tape_tracking.score_deduction = 0;
}

/**
 * @brief 更新胶带跟踪状态
 */
void MotionControl_UpdateTapeTracking(void)
{
    if (!tape_tracking.is_valid)
    {
        return;
    }

    uint32_t current_time = MotionControl_GetCurrentTime();
    Point_t current_pos = motion_control.current_position;

    // 检查是否在胶带上
    bool on_tape = MotionControl_IsOnTape(current_pos);
    float distance_to_tape = MotionControl_GetDistanceToTape(current_pos);

    if (!on_tape)
    {
        if (!tape_tracking.is_off_track)
        {
            // 刚开始脱轨
            tape_tracking.is_off_track = true;
            tape_tracking.off_track_start_time = current_time;
            motion_control.off_track_count++;
        }

        // 累计脱轨距离
        uint32_t off_track_duration = current_time - tape_tracking.off_track_start_time;
        if (off_track_duration > 100)
        {                                                                // 100ms更新一次
            tape_tracking.off_track_distance += distance_to_tape * 0.1f; // 假设0.1cm/100ms
            motion_control.off_track_distance = (uint32_t)tape_tracking.off_track_distance;

            // 检查扣分条件
            if (tape_tracking.off_track_distance >= 5.0f)
            {
                // 连续脱离5cm以上，记为0分
                tape_tracking.score_deduction = 100; // 100%扣分
            }
            else
            {
                // 每次脱离扣2分
                tape_tracking.score_deduction = motion_control.off_track_count * 2;
            }
        }
    }
    else
    {
        // 回到胶带上
        tape_tracking.is_off_track = false;
        tape_tracking.off_track_distance = 0;
    }

    tape_tracking.last_update_time = current_time;
}

/**
 * @brief 检查是否在胶带上
 */
bool MotionControl_IsOnTape(Point_t position)
{
    if (!tape_tracking.is_valid)
    {
        return false;
    }

    float distance = Rectangle_DistanceToEdge(tape_tracking.tape_rectangle, position);
    return distance <= motion_params.edge_tolerance;
}

/**
 * @brief 获取到胶带的距离
 */
float MotionControl_GetDistanceToTape(Point_t position)
{
    if (!tape_tracking.is_valid)
    {
        return INFINITY;
    }

    return Rectangle_DistanceToEdge(tape_tracking.tape_rectangle, position);
}

/**
 * @brief 获取运行时间
 */
uint32_t MotionControl_GetRuntime(void)
{
    if (motion_control.current_state != MOTION_STATE_IDLE)
    {
        return MotionControl_GetCurrentTime() - motion_control.state_start_time;
    }
    return motion_control.total_runtime;
}

/**
 * @brief 获取最大偏差
 */
float MotionControl_GetMaxDeviation(void)
{
    return motion_control.max_deviation;
}

/**
 * @brief 获取得分
 */
int MotionControl_GetScore(void)
{
    int base_score = 100;

    // 根据脱轨情况扣分
    if (tape_tracking.score_deduction >= 100)
    {
        return 0; // 连续脱离5cm以上
    }

    return base_score - tape_tracking.score_deduction;
}

/**
 * @brief 获取统计信息
 */
void MotionControl_GetStatistics(uint32_t *runtime, float *max_dev, int *score)
{
    if (runtime)
        *runtime = MotionControl_GetRuntime();
    if (max_dev)
        *max_dev = MotionControl_GetMaxDeviation();
    if (score)
        *score = MotionControl_GetScore();
}

/**
 * @brief 设置目标位置
 */
void MotionControl_SetTargetPosition(Point_t position)
{
    motion_control.target_position = position;
}

/**
 * @brief 获取当前位置
 */
Point_t MotionControl_GetCurrentPosition(void)
{
    return motion_control.current_position;
}

/**
 * @brief 获取目标位置
 */
Point_t MotionControl_GetTargetPosition(void)
{
    return motion_control.target_position;
}

/**
 * @brief 错误处理
 */
void MotionControl_HandleError(const char *error_msg)
{
    motion_control.emergency_stop = true;
    MotionControl_ChangeState(MOTION_STATE_ERROR);
    // 这里可以添加错误日志记录
}

/**
 * @brief 清除错误
 */
void MotionControl_ClearError(void)
{
    motion_control.emergency_stop = false;
    MotionControl_ChangeState(MOTION_STATE_IDLE);
}

/**
 * @brief 检查是否有错误
 */
bool MotionControl_HasError(void)
{
    return motion_control.current_state == MOTION_STATE_ERROR;
}
