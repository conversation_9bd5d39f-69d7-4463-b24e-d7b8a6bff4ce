#ifndef __COORDINATE_H_
#define __COORDINATE_H_

#include "sys.h"
#include <math.h>

// 坐标点结构体
typedef struct {
    float x;
    float y;
} Point_t;

// 矩形结构体
typedef struct {
    Point_t points[4];  // 四个顶点坐标
    Point_t center;     // 中心点
    float width;        // 宽度
    float height;       // 高度
    float angle;        // 旋转角度(弧度)
} Rectangle_t;

// 屏幕参数配置
typedef struct {
    float width;        // 屏幕宽度(cm)
    float height;       // 屏幕高度(cm)
    Point_t center;     // 屏幕中心点(cm)
    Point_t origin;     // 原点位置(cm)
} Screen_t;

// 舵机角度结构体
typedef struct {
    float x_angle;      // X轴舵机角度
    float y_angle;      // Y轴舵机角度
} ServoAngle_t;

// 路径点结构体
typedef struct {
    Point_t position;   // 位置坐标
    uint32_t time_ms;   // 到达时间(毫秒)
} PathPoint_t;

// 路径结构体
typedef struct {
    PathPoint_t points[200];  // 路径点数组
    uint16_t count;           // 路径点数量
    uint32_t total_time_ms;   // 总时间
} Path_t;

// 全局变量声明
extern Screen_t screen_config;
extern Point_t current_position;
extern Point_t target_position;

// 坐标转换函数
ServoAngle_t Point_To_ServoAngle(Point_t point);
Point_t ServoAngle_To_Point(ServoAngle_t angle);

// 几何运算函数
float Point_Distance(Point_t p1, Point_t p2);
float Point_Angle(Point_t p1, Point_t p2);
Point_t Point_Rotate(Point_t point, Point_t center, float angle);
Point_t Point_Interpolate(Point_t p1, Point_t p2, float ratio);

// 矩形处理函数
Rectangle_t Rectangle_Create(Point_t points[4]);
Rectangle_t Rectangle_CreateFromCenter(Point_t center, float width, float height, float angle);
void Rectangle_GetBoundary(Rectangle_t rect, Point_t boundary[], uint16_t *count);
bool Rectangle_IsPointInside(Rectangle_t rect, Point_t point);
float Rectangle_DistanceToEdge(Rectangle_t rect, Point_t point);

// 路径生成函数
void Path_Clear(Path_t *path);
void Path_AddPoint(Path_t *path, Point_t point, uint32_t time_ms);
void Path_GenerateRectangle(Path_t *path, Rectangle_t rect, uint32_t total_time_ms, bool clockwise);
void Path_GenerateScreenBorder(Path_t *path, uint32_t total_time_ms, bool clockwise);
void Path_GenerateReturnToOrigin(Path_t *path, Point_t start, uint32_t total_time_ms);

// 路径插值函数
PathPoint_t Path_GetCurrentTarget(Path_t *path, uint32_t current_time_ms);
bool Path_IsCompleted(Path_t *path, uint32_t current_time_ms);

// 系统初始化
void Coordinate_Init(void);

// 屏幕配置函数
void Screen_SetConfig(float width, float height, Point_t center, Point_t origin);

#endif // __COORDINATE_H_
